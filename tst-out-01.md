 npm test -- --testPathPattern="GovernanceTrackingBridge.test.ts" --verbose --coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=GovernanceTrackingBridge.test.ts --verbose --coverage

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts (1140.857 s, 58 MB heap size)
  GovernanceTrackingBridge Unit Tests
    Service Lifecycle Management
      ✓ should create bridge service instance successfully (6 ms)
      ✓ should initialize bridge service successfully (3 ms)
      ✓ should shutdown bridge service successfully (7 ms)
      ✓ should handle double initialization gracefully (3 ms)
    Bridge Initialization
      ✕ should initialize bridge with valid configuration (30005 ms)
      ✓ should reject initialization with invalid configuration (88 ms)
      ✕ should handle bridge initialization timeout gracefully (30007 ms)
    Governance Rules Synchronization
      ✕ should synchronize governance rules successfully (30005 ms)
      ✕ should handle empty rules array (30006 ms)
      ✕ should handle invalid rules gracefully (30007 ms)
      ✕ should handle partial synchronization failures (30006 ms)
    Tracking Data Forwarding
      ✕ should forward tracking data successfully (30012 ms)
      ✕ should validate tracking data before forwarding (30003 ms)
      ✕ should handle tracking data transformation (30005 ms)
    Cross-System Compliance Validation
      ✕ should validate cross-system compliance successfully (30005 ms)
      ✕ should handle validation scope with no systems (30003 ms)
    Event Handling
      ✕ should handle governance events successfully (30006 ms)
      ✕ should handle tracking events successfully (30005 ms)
      ✕ should handle invalid events gracefully (30006 ms)
    Health Monitoring
      ✕ should perform health check successfully (30005 ms)
      ✕ should get bridge metrics successfully (30004 ms)
      ✕ should detect unhealthy systems (30006 ms)
    Bridge Diagnostics
      ✕ should perform comprehensive diagnostics (30004 ms)
      ✕ should identify system check results (30003 ms)
      ✕ should provide actionable recommendations (30005 ms)
    Performance Validation
      ✕ should meet <5ms operation requirements (30004 ms)
      ✕ should handle concurrent operations efficiently (30005 ms)
      ✕ should maintain performance under load (30007 ms)
    Resilient Timing Integration
      ✕ should use resilient timing for all operations (30005 ms)
      ✕ should collect performance metrics (30005 ms)
      ✕ should handle timing failures gracefully (30004 ms)
    Memory Safety
      ✕ should not leak memory during repeated operations (30004 ms)
      ✕ should clean up resources on shutdown (30006 ms)
      ✕ should handle resource exhaustion gracefully (30003 ms)
    Error Handling and Recovery
      ✕ should handle network failures gracefully (30006 ms)
      ✕ should recover from temporary failures (30004 ms)
      ✕ should maintain service health after errors (30005 ms)
      ✕ should handle concurrent error scenarios (30006 ms)
    Authority Validation
      ✕ should validate authority data in tracking data (30006 ms)
      ✕ should handle missing authority data (30005 ms)
      ✕ should validate governance rule authority (30004 ms)
    Integration Readiness
      ✕ should be ready for integration after initialization (30005 ms)
      ✓ should support BaseTrackingService interface (4 ms)
      ✕ should handle service lifecycle correctly (30004 ms)

  ● GovernanceTrackingBridge Unit Tests › Bridge Initialization › should initialize bridge with valid configuration

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      471 |     });
      472 |
    > 473 |     test('should initialize bridge with valid configuration', async () => {
          |     ^
      474 |       const result = await bridge.initializeBridge(testBridgeConfig);
      475 |
      476 |       expect(result.success).toBe(true);

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:473:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:468:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Bridge Initialization › should handle bridge initialization timeout gracefully

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      490 |     });
      491 |
    > 492 |     test('should handle bridge initialization timeout gracefully', async () => {
          |     ^
      493 |       // Mock timeout scenario
      494 |       const timeoutConfig = {
      495 |         ...testBridgeConfig,

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:492:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:468:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Governance Rules Synchronization › should synchronize governance rules successfully

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      516 |
      517 |   describe('Governance Rules Synchronization', () => {
    > 518 |     beforeEach(async () => {
          |     ^
      519 |       await bridge.initialize();
      520 |       await bridge.initializeBridge(testBridgeConfig);
      521 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:518:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:517:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Governance Rules Synchronization › should handle empty rules array

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      516 |
      517 |   describe('Governance Rules Synchronization', () => {
    > 518 |     beforeEach(async () => {
          |     ^
      519 |       await bridge.initialize();
      520 |       await bridge.initializeBridge(testBridgeConfig);
      521 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:518:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:517:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Governance Rules Synchronization › should handle invalid rules gracefully

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      516 |
      517 |   describe('Governance Rules Synchronization', () => {
    > 518 |     beforeEach(async () => {
          |     ^
      519 |       await bridge.initialize();
      520 |       await bridge.initializeBridge(testBridgeConfig);
      521 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:518:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:517:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Governance Rules Synchronization › should handle partial synchronization failures

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      516 |
      517 |   describe('Governance Rules Synchronization', () => {
    > 518 |     beforeEach(async () => {
          |     ^
      519 |       await bridge.initialize();
      520 |       await bridge.initializeBridge(testBridgeConfig);
      521 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:518:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:517:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Tracking Data Forwarding › should forward tracking data successfully

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      564 |
      565 |   describe('Tracking Data Forwarding', () => {
    > 566 |     beforeEach(async () => {
          |     ^
      567 |       await bridge.initialize();
      568 |       await bridge.initializeBridge(testBridgeConfig);
      569 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:566:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:565:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Tracking Data Forwarding › should validate tracking data before forwarding

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      564 |
      565 |   describe('Tracking Data Forwarding', () => {
    > 566 |     beforeEach(async () => {
          |     ^
      567 |       await bridge.initialize();
      568 |       await bridge.initializeBridge(testBridgeConfig);
      569 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:566:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:565:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Tracking Data Forwarding › should handle tracking data transformation

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      564 |
      565 |   describe('Tracking Data Forwarding', () => {
    > 566 |     beforeEach(async () => {
          |     ^
      567 |       await bridge.initialize();
      568 |       await bridge.initializeBridge(testBridgeConfig);
      569 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:566:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:565:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Cross-System Compliance Validation › should validate cross-system compliance successfully

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      601 |
      602 |   describe('Cross-System Compliance Validation', () => {
    > 603 |     beforeEach(async () => {
          |     ^
      604 |       await bridge.initialize();
      605 |       await bridge.initializeBridge(testBridgeConfig);
      606 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:603:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:602:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Cross-System Compliance Validation › should handle validation scope with no systems

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      601 |
      602 |   describe('Cross-System Compliance Validation', () => {
    > 603 |     beforeEach(async () => {
          |     ^
      604 |       await bridge.initialize();
      605 |       await bridge.initializeBridge(testBridgeConfig);
      606 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:603:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:602:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Event Handling › should handle governance events successfully

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      649 |
      650 |   describe('Event Handling', () => {
    > 651 |     beforeEach(async () => {
          |     ^
      652 |       await bridge.initialize();
      653 |       await bridge.initializeBridge(testBridgeConfig);
      654 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:651:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:650:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Event Handling › should handle tracking events successfully

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      649 |
      650 |   describe('Event Handling', () => {
    > 651 |     beforeEach(async () => {
          |     ^
      652 |       await bridge.initialize();
      653 |       await bridge.initializeBridge(testBridgeConfig);
      654 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:651:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:650:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Event Handling › should handle invalid events gracefully

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      649 |
      650 |   describe('Event Handling', () => {
    > 651 |     beforeEach(async () => {
          |     ^
      652 |       await bridge.initialize();
      653 |       await bridge.initializeBridge(testBridgeConfig);
      654 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:651:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:650:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Health Monitoring › should perform health check successfully

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      710 |
      711 |   describe('Health Monitoring', () => {
    > 712 |     beforeEach(async () => {
          |     ^
      713 |       await bridge.initialize();
      714 |       await bridge.initializeBridge(testBridgeConfig);
      715 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:712:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:711:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Health Monitoring › should get bridge metrics successfully

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      710 |
      711 |   describe('Health Monitoring', () => {
    > 712 |     beforeEach(async () => {
          |     ^
      713 |       await bridge.initialize();
      714 |       await bridge.initializeBridge(testBridgeConfig);
      715 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:712:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:711:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Health Monitoring › should detect unhealthy systems

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      710 |
      711 |   describe('Health Monitoring', () => {
    > 712 |     beforeEach(async () => {
          |     ^
      713 |       await bridge.initialize();
      714 |       await bridge.initializeBridge(testBridgeConfig);
      715 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:712:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:711:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Bridge Diagnostics › should perform comprehensive diagnostics

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      761 |
      762 |   describe('Bridge Diagnostics', () => {
    > 763 |     beforeEach(async () => {
          |     ^
      764 |       await bridge.initialize();
      765 |       await bridge.initializeBridge(testBridgeConfig);
      766 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:763:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:762:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Bridge Diagnostics › should identify system check results

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      761 |
      762 |   describe('Bridge Diagnostics', () => {
    > 763 |     beforeEach(async () => {
          |     ^
      764 |       await bridge.initialize();
      765 |       await bridge.initializeBridge(testBridgeConfig);
      766 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:763:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:762:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Bridge Diagnostics › should provide actionable recommendations

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      761 |
      762 |   describe('Bridge Diagnostics', () => {
    > 763 |     beforeEach(async () => {
          |     ^
      764 |       await bridge.initialize();
      765 |       await bridge.initializeBridge(testBridgeConfig);
      766 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:763:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:762:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Performance Validation › should meet <5ms operation requirements

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      818 |
      819 |   describe('Performance Validation', () => {
    > 820 |     beforeEach(async () => {
          |     ^
      821 |       await bridge.initialize();
      822 |       await bridge.initializeBridge(testBridgeConfig);
      823 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:820:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:819:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Performance Validation › should handle concurrent operations efficiently

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      818 |
      819 |   describe('Performance Validation', () => {
    > 820 |     beforeEach(async () => {
          |     ^
      821 |       await bridge.initialize();
      822 |       await bridge.initializeBridge(testBridgeConfig);
      823 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:820:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:819:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Performance Validation › should maintain performance under load

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      818 |
      819 |   describe('Performance Validation', () => {
    > 820 |     beforeEach(async () => {
          |     ^
      821 |       await bridge.initialize();
      822 |       await bridge.initializeBridge(testBridgeConfig);
      823 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:820:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:819:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Resilient Timing Integration › should use resilient timing for all operations

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      878 |
      879 |   describe('Resilient Timing Integration', () => {
    > 880 |     beforeEach(async () => {
          |     ^
      881 |       await bridge.initialize();
      882 |       await bridge.initializeBridge(testBridgeConfig);
      883 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:880:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:879:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Resilient Timing Integration › should collect performance metrics

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      878 |
      879 |   describe('Resilient Timing Integration', () => {
    > 880 |     beforeEach(async () => {
          |     ^
      881 |       await bridge.initialize();
      882 |       await bridge.initializeBridge(testBridgeConfig);
      883 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:880:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:879:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Resilient Timing Integration › should handle timing failures gracefully

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      878 |
      879 |   describe('Resilient Timing Integration', () => {
    > 880 |     beforeEach(async () => {
          |     ^
      881 |       await bridge.initialize();
      882 |       await bridge.initializeBridge(testBridgeConfig);
      883 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:880:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:879:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Memory Safety › should not leak memory during repeated operations

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      918 |
      919 |   describe('Memory Safety', () => {
    > 920 |     beforeEach(async () => {
          |     ^
      921 |       await bridge.initialize();
      922 |       await bridge.initializeBridge(testBridgeConfig);
      923 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:920:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:919:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Memory Safety › should clean up resources on shutdown

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      918 |
      919 |   describe('Memory Safety', () => {
    > 920 |     beforeEach(async () => {
          |     ^
      921 |       await bridge.initialize();
      922 |       await bridge.initializeBridge(testBridgeConfig);
      923 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:920:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:919:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Memory Safety › should handle resource exhaustion gracefully

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      918 |
      919 |   describe('Memory Safety', () => {
    > 920 |     beforeEach(async () => {
          |     ^
      921 |       await bridge.initialize();
      922 |       await bridge.initializeBridge(testBridgeConfig);
      923 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:920:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:919:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Error Handling and Recovery › should handle network failures gracefully

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      975 |
      976 |   describe('Error Handling and Recovery', () => {
    > 977 |     beforeEach(async () => {
          |     ^
      978 |       await bridge.initialize();
      979 |       await bridge.initializeBridge(testBridgeConfig);
      980 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:977:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:976:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Error Handling and Recovery › should recover from temporary failures

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      975 |
      976 |   describe('Error Handling and Recovery', () => {
    > 977 |     beforeEach(async () => {
          |     ^
      978 |       await bridge.initialize();
      979 |       await bridge.initializeBridge(testBridgeConfig);
      980 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:977:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:976:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Error Handling and Recovery › should maintain service health after errors

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      975 |
      976 |   describe('Error Handling and Recovery', () => {
    > 977 |     beforeEach(async () => {
          |     ^
      978 |       await bridge.initialize();
      979 |       await bridge.initializeBridge(testBridgeConfig);
      980 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:977:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:976:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Error Handling and Recovery › should handle concurrent error scenarios

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      975 |
      976 |   describe('Error Handling and Recovery', () => {
    > 977 |     beforeEach(async () => {
          |     ^
      978 |       await bridge.initialize();
      979 |       await bridge.initializeBridge(testBridgeConfig);
      980 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:977:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:976:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Authority Validation › should validate authority data in tracking data

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      1041 |
      1042 |   describe('Authority Validation', () => {
    > 1043 |     beforeEach(async () => {
           |     ^
      1044 |       await bridge.initialize();
      1045 |       await bridge.initializeBridge(testBridgeConfig);
      1046 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:1043:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:1042:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Authority Validation › should handle missing authority data

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      1041 |
      1042 |   describe('Authority Validation', () => {
    > 1043 |     beforeEach(async () => {
           |     ^
      1044 |       await bridge.initialize();
      1045 |       await bridge.initializeBridge(testBridgeConfig);
      1046 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:1043:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:1042:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Authority Validation › should validate governance rule authority

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      1041 |
      1042 |   describe('Authority Validation', () => {
    > 1043 |     beforeEach(async () => {
           |     ^
      1044 |       await bridge.initialize();
      1045 |       await bridge.initializeBridge(testBridgeConfig);
      1046 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:1043:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:1042:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Integration Readiness › should be ready for integration after initialization

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      1077 |
      1078 |   describe('Integration Readiness', () => {
    > 1079 |     test('should be ready for integration after initialization', async () => {
           |     ^
      1080 |       await bridge.initialize();
      1081 |       await bridge.initializeBridge(testBridgeConfig);
      1082 |

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:1079:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:1078:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Integration Readiness › should handle service lifecycle correctly

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      1092 |     });
      1093 |
    > 1094 |     test('should handle service lifecycle correctly', async () => {
           |     ^
      1095 |       // Initialize
      1096 |       await bridge.initialize();
      1097 |       expect(bridge.isReady()).toBe(true);

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:1094:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:1078:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

-----------------------------|---------|----------|---------|---------|---------------------------------------------------------------------------------------
File                         | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s
-----------------------------|---------|----------|---------|---------|---------------------------------------------------------------------------------------
All files                    |      12 |     5.22 |    9.09 |   12.25 |
 GovernanceTrackingBridge.ts |      12 |     5.22 |    9.09 |   12.25 | 325-424,471,477,483,489,551-595,637-1698,1736,1740,1744,1749,1754,1759,1774,1782-2831
-----------------------------|---------|----------|---------|---------|---------------------------------------------------------------------------------------
Test Suites: 1 failed, 1 total
Tests:       38 failed, 6 passed, 44 total
Snapshots:   0 total
Time:        1141.499 s
Ran all test suites matching /GovernanceTrackingBridge.test.ts/i.