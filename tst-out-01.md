npm test -- --testPathPattern="GovernanceTrackingBridge.performance.test.ts" --verbose --detectOpenHandles --runInBand --
coverage

> oa-framework@1.0.0 test
> jest --testPathPattern=GovernanceTrackingBridge.performance.test.ts --verbose --detectOpenHandles --runInBand --

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.performance.test.ts (120.61 s, 45 MB heap size)
  GovernanceTrackingBridge Performance Tests
    Operation Latency Requirements
      ✓ should meet <5ms requirement for single operations (7 ms)
      ✓ should maintain <5ms average for batch operations (6 ms)
      ✓ should handle concurrent operations within latency limits (10 ms)
    Throughput Performance
      ✓ should achieve ≥1000 operations/second throughput (51 ms)
      ✕ should maintain throughput under sustained load (30168 ms)
    Memory Performance
      ✓ should not leak memory during repeated operations (10 ms)
      ✕ should maintain stable memory usage under load (30005 ms)
    Resilient Timing Performance
      ✓ should validate resilient timing overhead is minimal (12 ms)
      ✓ should collect performance metrics efficiently (7 ms)
    Stress Testing
      ✕ should handle extreme load gracefully (30012 ms)
      ✕ should recover performance after stress (30009 ms)

  ● GovernanceTrackingBridge Performance Tests › Throughput Performance › should maintain throughput under sustained load

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      517 |     });
      518 |
    > 519 |     test('should maintain throughput under sustained load', async () => {
          |     ^
      520 |       const sustainedDurationMs = 5000; // 5 seconds
      521 |       const operationsPerSecond = 500; // Target rate
      522 |       const intervalMs = 1000 / operationsPerSecond; // 2ms intervals

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.performance.test.ts:519:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.performance.test.ts:485:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.performance.test.ts:376:1)

  ● GovernanceTrackingBridge Performance Tests › Memory Performance › should maintain stable memory usage under load

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      585 |     });
      586 |
    > 587 |     test('should maintain stable memory usage under load', async () => {
          |     ^
      588 |       const measurements: number[] = [];
      589 |       const measurementInterval = 500; // 500ms
      590 |       const testDuration = 3000; // 3 seconds

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.performance.test.ts:587:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.performance.test.ts:560:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.performance.test.ts:376:1)

  ● GovernanceTrackingBridge Performance Tests › Stress Testing › should handle extreme load gracefully

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      684 |
      685 |   describe('Stress Testing', () => {
    > 686 |     test('should handle extreme load gracefully', async () => {
          |     ^
      687 |       const extremeLoadCount = 2000;
      688 |       const trackingDataArray = Array.from({ length: extremeLoadCount }, (_, i) =>
      689 |         createPerformanceTrackingData(i)

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.performance.test.ts:686:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.performance.test.ts:685:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.performance.test.ts:376:1)

  ● GovernanceTrackingBridge Performance Tests › Stress Testing › should recover performance after stress

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      718 |     });
      719 |
    > 720 |     test('should recover performance after stress', async () => {
          |     ^
      721 |       // Apply stress load
      722 |       const stressCount = 500;
      723 |       const stressOperations = Array.from({ length: stressCount }, (_, i) =>

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.performance.test.ts:720:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.performance.test.ts:685:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.performance.test.ts:376:1)

Test Suites: 1 failed, 1 total
Tests:       4 failed, 7 passed, 11 total
Snapshots:   0 total
Time:        120.873 s
Ran all test suites matching /GovernanceTrackingBridge.performance.test.ts/i.
